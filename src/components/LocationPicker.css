.location-picker-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  backdrop-filter: blur(4px);
}

.location-picker {
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 500px;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.location-picker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #eee;
  background: #f8f9fa;
}

.location-picker-header h3 {
  margin: 0;
  color: #333;
  font-size: 18px;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background: #e9ecef;
  color: #333;
}

.location-picker-content {
  padding: 20px;
  max-height: calc(80vh - 80px);
  overflow-y: auto;
}

.search-section,
.custom-section,
.current-section {
  margin-bottom: 24px;
}

.search-section h4,
.custom-section h4,
.current-section h4 {
  margin: 0 0 12px 0;
  color: #333;
  font-size: 14px;
  font-weight: 600;
}

.search-input {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 14px;
  margin-bottom: 12px;
  outline: none;
  transition: border-color 0.2s ease;
}

.search-input:focus {
  border-color: #4285f4;
  box-shadow: 0 0 0 3px rgba(66, 133, 244, 0.1);
}

.locations-list {
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #eee;
  border-radius: 8px;
}

.location-item {
  padding: 12px 16px;
  cursor: pointer;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.2s ease;
}

.location-item:last-child {
  border-bottom: none;
}

.location-item:hover {
  background: #f8f9fa;
}

.location-name {
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.location-coords {
  font-size: 12px;
  color: #666;
  font-family: monospace;
}

.coords-inputs {
  display: flex;
  gap: 12px;
  margin-bottom: 12px;
}

.coord-input {
  flex: 1;
}

.coord-input label {
  display: block;
  margin-bottom: 4px;
  font-size: 12px;
  color: #666;
  font-weight: 500;
}

.coord-input input {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  outline: none;
  transition: border-color 0.2s ease;
}

.coord-input input:focus {
  border-color: #4285f4;
  box-shadow: 0 0 0 3px rgba(66, 133, 244, 0.1);
}

.apply-coords-btn {
  width: 100%;
  padding: 12px;
  background: #4285f4;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.apply-coords-btn:hover {
  background: #3367d6;
}

.current-location {
  padding: 12px 16px;
  background: #f8f9fa;
  border-radius: 8px;
  font-family: monospace;
  font-size: 14px;
  color: #333;
  border: 1px solid #e9ecef;
}

/* Scrollbar styling */
.locations-list::-webkit-scrollbar,
.location-picker-content::-webkit-scrollbar {
  width: 6px;
}

.locations-list::-webkit-scrollbar-track,
.location-picker-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.locations-list::-webkit-scrollbar-thumb,
.location-picker-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.locations-list::-webkit-scrollbar-thumb:hover,
.location-picker-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Responsive design */
@media (max-width: 600px) {
  .location-picker {
    width: 95%;
    margin: 20px;
  }
  
  .coords-inputs {
    flex-direction: column;
    gap: 8px;
  }
  
  .location-picker-header {
    padding: 16px;
  }
  
  .location-picker-content {
    padding: 16px;
  }
}
