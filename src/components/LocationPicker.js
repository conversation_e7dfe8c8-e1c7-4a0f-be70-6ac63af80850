import React, { useState } from 'react';
import './LocationPicker.css';

const LocationPicker = ({ 
  currentLocation, 
  onLocationChange, 
  onClose,
  predefinedLocations = []
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [customLat, setCustomLat] = useState(currentLocation?.lat?.toString() || '');
  const [customLng, setCustomLng] = useState(currentLocation?.lng?.toString() || '');

  const defaultLocations = [
    { name: 'San Francisco, CA', lat: 37.7749, lng: -122.4194 },
    { name: 'New York, NY', lat: 40.7128, lng: -74.0060 },
    { name: 'Los Angeles, CA', lat: 34.0522, lng: -118.2437 },
    { name: 'Chicago, IL', lat: 41.8781, lng: -87.6298 },
    { name: 'Miami, FL', lat: 25.7617, lng: -80.1918 },
    { name: 'Seattle, WA', lat: 47.6062, lng: -122.3321 },
    { name: 'Austin, TX', lat: 30.2672, lng: -97.7431 },
    { name: 'Boston, MA', lat: 42.3601, lng: -71.0589 },
  ];

  const allLocations = [...predefinedLocations, ...defaultLocations];
  
  const filteredLocations = allLocations.filter(location =>
    location.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleLocationSelect = (location) => {
    onLocationChange(location);
    onClose();
  };

  const handleCustomLocationSubmit = () => {
    const lat = parseFloat(customLat);
    const lng = parseFloat(customLng);
    
    if (isNaN(lat) || isNaN(lng)) {
      alert('Please enter valid latitude and longitude values');
      return;
    }

    if (lat < -90 || lat > 90) {
      alert('Latitude must be between -90 and 90');
      return;
    }

    if (lng < -180 || lng > 180) {
      alert('Longitude must be between -180 and 180');
      return;
    }

    onLocationChange({ lat, lng });
    onClose();
  };

  return (
    <div className="location-picker-overlay">
      <div className="location-picker">
        <div className="location-picker-header">
          <h3>📍 Select Location</h3>
          <button className="close-btn" onClick={onClose}>×</button>
        </div>

        <div className="location-picker-content">
          {/* Search Section */}
          <div className="search-section">
            <h4>🔍 Search Locations</h4>
            <input
              type="text"
              placeholder="Search for a city or location..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="search-input"
            />
            
            <div className="locations-list">
              {filteredLocations.slice(0, 6).map((location, index) => (
                <div
                  key={index}
                  className="location-item"
                  onClick={() => handleLocationSelect(location)}
                >
                  <div className="location-name">{location.name}</div>
                  <div className="location-coords">
                    {location.lat.toFixed(4)}, {location.lng.toFixed(4)}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Custom Coordinates Section */}
          <div className="custom-section">
            <h4>🎯 Custom Coordinates</h4>
            <div className="coords-inputs">
              <div className="coord-input">
                <label>Latitude:</label>
                <input
                  type="number"
                  step="any"
                  placeholder="37.7749"
                  value={customLat}
                  onChange={(e) => setCustomLat(e.target.value)}
                />
              </div>
              <div className="coord-input">
                <label>Longitude:</label>
                <input
                  type="number"
                  step="any"
                  placeholder="-122.4194"
                  value={customLng}
                  onChange={(e) => setCustomLng(e.target.value)}
                />
              </div>
            </div>
            <button 
              className="apply-coords-btn"
              onClick={handleCustomLocationSubmit}
            >
              Apply Coordinates
            </button>
          </div>

          {/* Current Location Section */}
          {currentLocation && (
            <div className="current-section">
              <h4>📌 Current Location</h4>
              <div className="current-location">
                {currentLocation.lat.toFixed(4)}, {currentLocation.lng.toFixed(4)}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default LocationPicker;
